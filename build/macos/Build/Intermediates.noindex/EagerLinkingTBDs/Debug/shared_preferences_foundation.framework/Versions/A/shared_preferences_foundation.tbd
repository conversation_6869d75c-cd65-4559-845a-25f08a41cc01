{"main_library": {"exported_symbols": [{"data": {"global": ["_$s29shared_preferences_foundation29LegacySharedPreferencesPluginCfD", "_$s29shared_preferences_foundation21LegacyUserDefaultsApiMp", "_$s29shared_preferences_foundation20UserDefaultsApiSetupCMa", "_$s29shared_preferences_foundation11PigeonErrorC7detailsypSgvpMV", "_$s29shared_preferences_foundation29LegacySharedPreferencesPluginC5clear6prefix9allowListSbSS_SaySSGSgtF", "_$s29shared_preferences_foundation13argumentErrorSSvau", "_$s29shared_preferences_foundation20UserDefaultsApiSetupC5setUp15binaryMessenger3api20messageChannelSuffixySo013FlutterBinaryK0_p_AA0deF0_pSgSStFZfA1_", "_$s29shared_preferences_foundation30SharedPreferencesPigeonOptionsV9suiteNameACSSSg_tcfC", "_$s29shared_preferences_foundation20UserDefaultsApiSetupCMm", "_$s29shared_preferences_foundation23SharedPreferencesPluginCACycfc", "_$s29shared_preferences_foundation29LegacySharedPreferencesPluginC9setDouble3key5valueySS_SdtFTq", "_$s29shared_preferences_foundation29LegacySharedPreferencesPluginCMn", "_shared_preferences_foundationVersionString", "_$s29shared_preferences_foundation19MessagesPigeonCodecCMn", "_$s29shared_preferences_foundation23SharedPreferencesPluginC3set3key5value7optionsySS_ypAA0dE13PigeonOptionsVtKF", "_$s29shared_preferences_foundation30SharedPreferencesPigeonOptionsV8fromListyACSgSayypSgGFZ", "_$s29shared_preferences_foundation19MessagesPigeonCodecCMa", "_$s29shared_preferences_foundation30SharedPreferencesPigeonOptionsV9suiteNameSSSgvM", "_$s29shared_preferences_foundation20UserDefaultsApiSetupCfD", "_$s29shared_preferences_foundation11PigeonErrorCMn", "_$s29shared_preferences_foundation19MessagesPigeonCodecCN", "_$s29shared_preferences_foundation23SharedPreferencesPluginC6remove3key7optionsySS_AA0dE13PigeonOptionsVtKF", "_$s29shared_preferences_foundation29LegacySharedPreferencesPluginC6getAll6prefix9allowListSDySSypGSS_SaySSGSgtFTq", "_$s29shared_preferences_foundation26LegacyUserDefaultsApiSetupC5setUp15binaryMessenger3api20messageChannelSuffixySo013FlutterBinaryL0_p_AA0defG0_pSgSStFZfA1_", "_$s29shared_preferences_foundation30SharedPreferencesPigeonOptionsV9suiteNameSSSgvpfi", "_$s29shared_preferences_foundation11PigeonErrorCs0E0AAMc", "_$s29shared_preferences_foundation15UserDefaultsApiMp", "_$s29shared_preferences_foundation23SharedPreferencesPluginC6getAll9allowList7optionsSDySSypGSaySSGSg_AA0dE13PigeonOptionsVtKF", "_$s29shared_preferences_foundation11PigeonErrorC7messageSSSgvg", "_$s29shared_preferences_foundation15UserDefaultsApiTL", "_$s29shared_preferences_foundation11PigeonErrorC4codeSSvpWvd", "_$s29shared_preferences_foundation21LegacyUserDefaultsApiTL", "_$s29shared_preferences_foundation29LegacySharedPreferencesPluginC8register4withySo07FlutterG9Registrar_p_tFZ", "_$s29shared_preferences_foundation20UserDefaultsApiSetupCN", "_$s29shared_preferences_foundation23SharedPreferencesPluginC16isTypeCompatible5valueSbyp_tFZ", "_$s29shared_preferences_foundation26LegacyUserDefaultsApiSetupCACycfCTq", "_$s29shared_preferences_foundation11PigeonErrorC4code7message7detailsACSS_SSSgypSgtcfC", "_$s29shared_preferences_foundation26LegacyUserDefaultsApiSetupC5setUp15binaryMessenger3api20messageChannelSuffixySo013FlutterBinaryL0_p_AA0defG0_pSgSStFZ", "_$s29shared_preferences_foundation23SharedPreferencesPluginC8register4withySo07FlutterF9Registrar_p_tFZ", "_$s29shared_preferences_foundation13argumentErrorSSvp", "_$s29shared_preferences_foundation20UserDefaultsApiSetupC5codecSo27FlutterStandardMessageCodecCvgZ", "_shared_preferences_foundationVersionNumber", "_$s29shared_preferences_foundation26LegacyUserDefaultsApiSetupCACycfc", "_$s29shared_preferences_foundation19MessagesPigeonCodecC0A0ACvau", "_$s29shared_preferences_foundation29LegacySharedPreferencesPluginC7setBool3key5valueySS_SbtF", "_$s29shared_preferences_foundation26LegacyUserDefaultsApiSetupC5codecSo27FlutterStandardMessageCodecCvpZMV", "_$s29shared_preferences_foundation19MessagesPigeonCodecC0A0ACvpZ", "_$s29shared_preferences_foundation29LegacySharedPreferencesPluginC7setBool3key5valueySS_SbtFTq", "_$s29shared_preferences_foundation11PigeonErrorCN", "_$s29shared_preferences_foundation11PigeonErrorC20localizedDescriptionSSvpMV", "_$s29shared_preferences_foundation23SharedPreferencesPluginCAA15UserDefaultsApiAAMc", "_$s29shared_preferences_foundation30SharedPreferencesPigeonOptionsV9suiteNameSSSgvs", "_$s29shared_preferences_foundation11PigeonErrorC4codeSSvg", "_$s29shared_preferences_foundation11PigeonErrorC20localizedDescriptionSSvg", "_$s29shared_preferences_foundation26LegacyUserDefaultsApiSetupCACycfC", "_$s29shared_preferences_foundation30SharedPreferencesPigeonOptionsV9suiteNameACSSSg_tcfcfA_", "_$s29shared_preferences_foundation11PigeonErrorC7messageSSSgvpWvd", "_$s29shared_preferences_foundation29LegacySharedPreferencesPluginCAA0D15UserDefaultsApiAAWP", "_$s29shared_preferences_foundation11PigeonErrorC7detailsypSgvg", "_$s29shared_preferences_foundation11PigeonErrorC7detailsypSgvpWvd", "_$s29shared_preferences_foundation23SharedPreferencesPluginCMn", "_$s29shared_preferences_foundation11PigeonErrorC4code7message7detailsACSS_SSSgypSgtcfCTq", "_$s29shared_preferences_foundation23SharedPreferencesPluginC7getKeys9allowList7optionsSaySSGAGSg_AA0dE13PigeonOptionsVtKF", "_$s29shared_preferences_foundation19MessagesPigeonCodecCfD", "_$s29shared_preferences_foundation19MessagesPigeonCodecCACycfc", "_$s29shared_preferences_foundation29LegacySharedPreferencesPluginC6remove3keyySS_tFTq", "_$s29shared_preferences_foundation29LegacySharedPreferencesPluginCMa", "_$s29shared_preferences_foundation29LegacySharedPreferencesPluginC8setValue3key5valueySS_yptF", "_$s29shared_preferences_foundation30SharedPreferencesPigeonOptionsVMn", "_$s29shared_preferences_foundation23SharedPreferencesPluginCMa", "_$s29shared_preferences_foundation30SharedPreferencesPigeonOptionsV9suiteNameSSSgvpMV", "_$s29shared_preferences_foundation23SharedPreferencesPluginC3set3key5value7optionsySS_ypAA0dE13PigeonOptionsVtKFTq", "_$s29shared_preferences_foundation29LegacySharedPreferencesPluginC6getAll6prefix9allowListSDySSypGSS_SaySSGSgtF", "_$s29shared_preferences_foundation30SharedPreferencesPigeonOptionsV6toListSayypSgGyF", "_$s29shared_preferences_foundation29LegacySharedPreferencesPluginC8setValue3key5valueySS_yptFTq", "_$s29shared_preferences_foundation23SharedPreferencesPluginC8getValue3key7optionsypSgSS_AA0dE13PigeonOptionsVtKFTq", "_$s29shared_preferences_foundation26LegacyUserDefaultsApiSetupC5codecSo27FlutterStandardMessageCodecCvgZ", "_$s29shared_preferences_foundation29LegacySharedPreferencesPluginC5clear6prefix9allowListSbSS_SaySSGSgtFTq", "_$s29shared_preferences_foundation11PigeonErrorC4code7message7detailsACSS_SSSgypSgtcfc", "_$s29shared_preferences_foundation19MessagesPigeonCodecC0A0ACvgZ", "_$s29shared_preferences_foundation20UserDefaultsApiSetupCfd", "_$s29shared_preferences_foundation26LegacyUserDefaultsApiSetupCfd", "_$s29shared_preferences_foundation26LegacyUserDefaultsApiSetupCMm", "_$s29shared_preferences_foundation30SharedPreferencesPigeonOptionsVACycfC", "_$s29shared_preferences_foundation11PigeonErrorCfD", "_$s29shared_preferences_foundation23SharedPreferencesPluginCACycfC", "_$s29shared_preferences_foundation23SharedPreferencesPluginC11getAllPrefs9allowList7optionsSDySSypGSaySSGSg_AA0dE13PigeonOptionsVtKFZ", "_$s29shared_preferences_foundation11PigeonErrorC4codeSSvpMV", "_$s29shared_preferences_foundation20UserDefaultsApiSetupCACycfCTq", "_$s29shared_preferences_foundation26LegacyUserDefaultsApiSetupCMa", "_$s29shared_preferences_foundation30SharedPreferencesPigeonOptionsVN", "_$s29shared_preferences_foundation23SharedPreferencesPluginC5clear9allowList7optionsySaySSGSg_AA0dE13PigeonOptionsVtKF", "_$s29shared_preferences_foundation20UserDefaultsApiSetupC5codecSo27FlutterStandardMessageCodecCvpZMV", "_$s29shared_preferences_foundation19MessagesPigeonCodecCACycfC", "_$s29shared_preferences_foundation23SharedPreferencesPluginCN", "_$s29shared_preferences_foundation23SharedPreferencesPluginCfD", "_$s29shared_preferences_foundation29LegacySharedPreferencesPluginC11getAllPrefs6prefix9allowListSDySSypGSS_SaySSGSgtF", "_$s29shared_preferences_foundation11PigeonErrorCfd", "_$s29shared_preferences_foundation23SharedPreferencesPluginC6getAll9allowList7optionsSDySSypGSaySSGSg_AA0dE13PigeonOptionsVtKFTq", "_$s29shared_preferences_foundation29LegacySharedPreferencesPluginCACycfc", "_$s29shared_preferences_foundation19MessagesPigeonCodecC0A0ACvpZMV", "_$s29shared_preferences_foundation11PigeonErrorC7messageSSSgvpMV", "_$s29shared_preferences_foundation26LegacyUserDefaultsApiSetupCfD", "_$s29shared_preferences_foundation11PigeonErrorCMm", "_$s29shared_preferences_foundation20UserDefaultsApiSetupCACycfc", "_$s29shared_preferences_foundation23SharedPreferencesPluginC6remove3key7optionsySS_AA0dE13PigeonOptionsVtKFTq", "_$s29shared_preferences_foundation20UserDefaultsApiSetupCACycfC", "_$s29shared_preferences_foundation23SharedPreferencesPluginCAA15UserDefaultsApiAAWP", "_$s29shared_preferences_foundation23SharedPreferencesPluginC7getKeys9allowList7optionsSaySSGAGSg_AA0dE13PigeonOptionsVtKFTq", "_$s29shared_preferences_foundation23SharedPreferencesPluginC5clear9allowList7optionsySaySSGSg_AA0dE13PigeonOptionsVtKFTq", "_$s29shared_preferences_foundation29LegacySharedPreferencesPluginCAA0D15UserDefaultsApiAAMc", "_$s29shared_preferences_foundation11PigeonErrorCMa", "_$s29shared_preferences_foundation20UserDefaultsApiSetupC5setUp15binaryMessenger3api20messageChannelSuffixySo013FlutterBinaryK0_p_AA0deF0_pSgSStFZ", "_$s29shared_preferences_foundation30SharedPreferencesPigeonOptionsV9suiteNameSSSgvg", "_$s29shared_preferences_foundation30SharedPreferencesPigeonOptionsVMa", "_$s29shared_preferences_foundation23SharedPreferencesPluginC8getValue3key7optionsypSgSS_AA0dE13PigeonOptionsVtKF", "_$s29shared_preferences_foundation20UserDefaultsApiSetupCMn", "_$s29shared_preferences_foundation26LegacyUserDefaultsApiSetupCN", "_$s29shared_preferences_foundation29LegacySharedPreferencesPluginCN", "_$s29shared_preferences_foundation29LegacySharedPreferencesPluginC6remove3keyySS_tF", "_$s29shared_preferences_foundation29LegacySharedPreferencesPluginC9setDouble3key5valueySS_SdtF", "_$s29shared_preferences_foundation26LegacyUserDefaultsApiSetupCMn", "_$s29shared_preferences_foundation29LegacySharedPreferencesPluginCACycfC", "_$s29shared_preferences_foundation29LegacySharedPreferencesPluginC11getAllPrefs6prefix9allowListSDySSypGSS_SaySSGSgtFTq"], "objc_class": ["_TtC29shared_preferences_foundation29LegacySharedPreferencesPlugin", "PodsDummy_shared_preferences_foundation", "_TtC29shared_preferences_foundation19MessagesPigeonCodec", "_TtC29shared_preferences_foundation23SharedPreferencesPlugin"]}}], "flags": [{"attributes": ["not_app_extension_safe"]}], "install_names": [{"name": "@rpath/shared_preferences_foundation.framework/Versions/A/shared_preferences_foundation"}], "swift_abi": [{"abi": 7}], "target_info": [{"min_deployment": "11", "target": "arm64-macos"}]}, "tapi_tbd_version": 5}