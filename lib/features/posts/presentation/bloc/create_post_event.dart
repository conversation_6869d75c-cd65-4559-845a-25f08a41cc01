import 'package:equatable/equatable.dart';

abstract class CreatePostEvent extends Equatable {
  const CreatePostEvent();

  @override
  List<Object?> get props => [];
}

class CreatePostSubmitted extends CreatePostEvent {
  final String title;
  final String body;
  final int userId;

  const CreatePostSubmitted({
    required this.title,
    required this.body,
    required this.userId,
  });

  @override
  List<Object?> get props => [title, body, userId];
}

class CreatePostReset extends CreatePostEvent {}
