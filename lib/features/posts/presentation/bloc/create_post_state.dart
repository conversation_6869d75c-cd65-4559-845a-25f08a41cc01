import 'package:equatable/equatable.dart';
import 'package:user_management_app/features/users/domain/entities/post.dart';

abstract class CreatePostState extends Equatable {
  const CreatePostState();

  @override
  List<Object?> get props => [];
}

class CreatePostInitial extends CreatePostState {}

class CreatePostLoading extends CreatePostState {}

class CreatePostSuccess extends CreatePostState {
  final Post post;

  const CreatePostSuccess(this.post);

  @override
  List<Object?> get props => [post];
}

class CreatePostError extends CreatePostState {
  final String message;

  const CreatePostError(this.message);

  @override
  List<Object?> get props => [message];
}
