import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:user_management_app/features/users/domain/repositories/users_repository.dart';
import 'package:user_management_app/features/posts/presentation/bloc/create_post_event.dart';
import 'package:user_management_app/features/posts/presentation/bloc/create_post_state.dart';

class CreatePostBloc extends Bloc<CreatePostEvent, CreatePostState> {
  final UsersRepository repository;

  CreatePostBloc({required this.repository}) : super(CreatePostInitial()) {
    on<CreatePostSubmitted>(_onCreatePostSubmitted);
    on<CreatePostReset>(_onCreatePostReset);
  }

  Future<void> _onCreatePostSubmitted(
    CreatePostSubmitted event,
    Emitter<CreatePostState> emit,
  ) async {
    emit(CreatePostLoading());
    try {
      final post = await repository.createPost(
        title: event.title,
        body: event.body,
        userId: event.userId,
      );
      emit(CreatePostSuccess(post));
    } catch (e) {
      emit(CreatePostError(e.toString()));
    }
  }

  void _onCreatePostReset(
    CreatePostReset event,
    Emitter<CreatePostState> emit,
  ) {
    emit(CreatePostInitial());
  }
}
