import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:user_management_app/features/users/domain/repositories/users_repository.dart';
import 'package:user_management_app/features/users/presentation/bloc/users_event.dart';
import 'package:user_management_app/features/users/presentation/bloc/users_state.dart';
import 'package:user_management_app/features/users/domain/entities/user.dart';
import 'package:user_management_app/core/constants/api_constants.dart';

class UsersBloc extends Bloc<UsersEvent, UsersState> {
  final UsersRepository repository;
  
  UsersBloc({required this.repository}) : super(UsersInitial()) {
    on<LoadUsersEvent>(_onLoadUsers);
    on<LoadMoreUsersEvent>(_onLoadMoreUsers);
    on<SearchUsersEvent>(_onSearchUsers);
    on<ClearSearchEvent>(_onClearSearch);
    on<RefreshUsersEvent>(_onRefreshUsers);
    on<LoadUserDetailsEvent>(_onLoadUserDetails);
  }

  Future<void> _onLoadUsers(LoadUsersEvent event, Emitter<UsersState> emit) async {
    emit(UsersLoading());
    try {
      final users = await repository.getUsers(
        limit: ApiConstants.defaultLimit,
        skip: ApiConstants.defaultSkip,
      );
      emit(UsersLoaded(
        users: users,
        hasReachedMax: users.length < ApiConstants.defaultLimit,
      ));
    } catch (e) {
      emit(UsersError(e.toString()));
    }
  }

  Future<void> _onLoadMoreUsers(LoadMoreUsersEvent event, Emitter<UsersState> emit) async {
    final currentState = state;
    if (currentState is UsersLoaded && !currentState.hasReachedMax && !currentState.isLoadingMore) {
      emit(currentState.copyWith(isLoadingMore: true));
      
      try {
        final newUsers = await repository.getUsers(
          limit: ApiConstants.defaultLimit,
          skip: currentState.users.length,
        );
        
        final allUsers = List<User>.from(currentState.users)..addAll(newUsers);
        
        emit(UsersLoaded(
          users: allUsers,
          hasReachedMax: newUsers.length < ApiConstants.defaultLimit,
          isLoadingMore: false,
          searchQuery: currentState.searchQuery,
        ));
      } catch (e) {
        emit(currentState.copyWith(isLoadingMore: false));
      }
    }
  }

  Future<void> _onSearchUsers(SearchUsersEvent event, Emitter<UsersState> emit) async {
    if (event.query.isEmpty) {
      add(ClearSearchEvent());
      return;
    }

    emit(UsersLoading());
    try {
      final users = await repository.searchUsers(event.query);
      emit(UsersLoaded(
        users: users,
        hasReachedMax: true, // Search results don't support pagination
        searchQuery: event.query,
      ));
    } catch (e) {
      emit(UsersError(e.toString()));
    }
  }

  Future<void> _onClearSearch(ClearSearchEvent event, Emitter<UsersState> emit) async {
    add(LoadUsersEvent());
  }

  Future<void> _onRefreshUsers(RefreshUsersEvent event, Emitter<UsersState> emit) async {
    final currentState = state;
    if (currentState is UsersLoaded && currentState.searchQuery != null) {
      add(SearchUsersEvent(currentState.searchQuery!));
    } else {
      add(LoadUsersEvent());
    }
  }

  Future<void> _onLoadUserDetails(LoadUserDetailsEvent event, Emitter<UsersState> emit) async {
    emit(UserDetailsLoading());
    try {
      // Find user from current state or fetch if needed
      User? user;
      final currentState = state;
      if (currentState is UsersLoaded) {
        user = currentState.users.firstWhere(
          (u) => u.id == event.userId,
          orElse: () => throw Exception('User not found'),
        );
      }

      if (user == null) {
        throw Exception('User not found');
      }

      // Fetch posts and todos concurrently
      final results = await Future.wait([
        repository.getUserPosts(event.userId),
        repository.getUserTodos(event.userId),
      ]);

      emit(UserDetailsLoaded(
        user: user,
        posts: results[0] as List,
        todos: results[1] as List,
      ));
    } catch (e) {
      emit(UserDetailsError(e.toString()));
    }
  }
}
