import 'package:equatable/equatable.dart';

abstract class UsersEvent extends Equatable {
  const UsersEvent();

  @override
  List<Object?> get props => [];
}

class LoadUsersEvent extends UsersEvent {}

class LoadMoreUsersEvent extends UsersEvent {}

class SearchUsersEvent extends UsersEvent {
  final String query;

  const SearchUsersEvent(this.query);

  @override
  List<Object?> get props => [query];
}

class ClearSearchEvent extends UsersEvent {}

class RefreshUsersEvent extends UsersEvent {}

class LoadUserDetailsEvent extends UsersEvent {
  final int userId;

  const LoadUserDetailsEvent(this.userId);

  @override
  List<Object?> get props => [userId];
}
