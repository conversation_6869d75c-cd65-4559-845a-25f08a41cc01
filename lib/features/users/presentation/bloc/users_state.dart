import 'package:equatable/equatable.dart';
import 'package:user_management_app/features/users/domain/entities/user.dart';
import 'package:user_management_app/features/users/domain/entities/post.dart';
import 'package:user_management_app/features/users/domain/entities/todo.dart';

abstract class UsersState extends Equatable {
  const UsersState();

  @override
  List<Object?> get props => [];
}

class UsersInitial extends UsersState {}

class UsersLoading extends UsersState {}

class UsersLoaded extends UsersState {
  final List<User> users;
  final bool hasReachedMax;
  final bool isLoadingMore;
  final String? searchQuery;

  const UsersLoaded({
    required this.users,
    this.hasReachedMax = false,
    this.isLoadingMore = false,
    this.searchQuery,
  });

  UsersLoaded copyWith({
    List<User>? users,
    bool? hasReachedMax,
    bool? isLoadingMore,
    String? searchQuery,
  }) {
    return UsersLoaded(
      users: users ?? this.users,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }

  @override
  List<Object?> get props => [users, hasReachedMax, isLoadingMore, searchQuery];
}

class UsersError extends UsersState {
  final String message;

  const UsersError(this.message);

  @override
  List<Object?> get props => [message];
}

class UserDetailsLoading extends UsersState {}

class UserDetailsLoaded extends UsersState {
  final User user;
  final List<Post> posts;
  final List<Todo> todos;

  const UserDetailsLoaded({
    required this.user,
    required this.posts,
    required this.todos,
  });

  @override
  List<Object?> get props => [user, posts, todos];
}

class UserDetailsError extends UsersState {
  final String message;

  const UserDetailsError(this.message);

  @override
  List<Object?> get props => [message];
}
