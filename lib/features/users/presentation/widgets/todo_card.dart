import 'package:flutter/material.dart';
import 'package:user_management_app/features/users/domain/entities/todo.dart';

class TodoCard extends StatelessWidget {
  final Todo todo;

  const TodoCard({super.key, required this.todo});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: todo.completed 
              ? Colors.green.withOpacity(0.3)
              : Colors.grey.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Checkbox
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: todo.completed 
                  ? Colors.green 
                  : Colors.transparent,
              border: Border.all(
                color: todo.completed 
                    ? Colors.green 
                    : Colors.grey.shade400,
                width: 2,
              ),
            ),
            child: todo.completed
                ? const Icon(
                    Icons.check,
                    size: 16,
                    color: Colors.white,
                  )
                : null,
          ),
          
          const SizedBox(width: 16),
          
          // Todo Text
          Expanded(
            child: Text(
              todo.todo,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                decoration: todo.completed 
                    ? TextDecoration.lineThrough 
                    : null,
                color: todo.completed 
                    ? Theme.of(context).textTheme.bodySmall?.color
                    : null,
              ),
            ),
          ),
          
          // Status Badge
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 8,
              vertical: 4,
            ),
            decoration: BoxDecoration(
              color: todo.completed 
                  ? Colors.green.withOpacity(0.1)
                  : Colors.orange.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              todo.completed ? 'Done' : 'Pending',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: todo.completed ? Colors.green : Colors.orange,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
