import 'package:user_management_app/features/users/domain/entities/user.dart';
import 'package:user_management_app/features/users/domain/entities/post.dart';
import 'package:user_management_app/features/users/domain/entities/todo.dart';

abstract class UsersRepository {
  Future<List<User>> getUsers({int limit = 20, int skip = 0});
  Future<List<User>> searchUsers(String query);
  Future<List<Post>> getUserPosts(int userId);
  Future<List<Todo>> getUserTodos(int userId);
  Future<Post> createPost({required String title, required String body, required int userId});
}
