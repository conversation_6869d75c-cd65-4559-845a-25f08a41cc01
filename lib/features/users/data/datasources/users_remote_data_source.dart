import 'package:user_management_app/core/network/api_client.dart';
import 'package:user_management_app/core/constants/api_constants.dart';
import 'package:user_management_app/features/users/data/models/user_model.dart';
import 'package:user_management_app/features/users/data/models/post_model.dart';
import 'package:user_management_app/features/users/data/models/todo_model.dart';

class UsersRemoteDataSource {
  final ApiClient apiClient;

  UsersRemoteDataSource({required this.apiClient});

  Future<List<UserModel>> getUsers({int limit = 20, int skip = 0}) async {
    try {
      final response = await apiClient.get(
        ApiConstants.usersEndpoint,
        queryParams: {
          'limit': limit.toString(),
          'skip': skip.toString(),
        },
      );

      final List<dynamic> usersJson = response['users'] ?? [];
      return usersJson.map((json) => UserModel.fromJson(json)).toList();
    } catch (e) {
      throw Exception('Failed to fetch users: $e');
    }
  }

  Future<List<UserModel>> searchUsers(String query) async {
    try {
      final response = await apiClient.get(
        ApiConstants.searchUsersEndpoint,
        queryParams: {'q': query},
      );

      final List<dynamic> usersJson = response['users'] ?? [];
      return usersJson.map((json) => UserModel.fromJson(json)).toList();
    } catch (e) {
      throw Exception('Failed to search users: $e');
    }
  }

  Future<List<PostModel>> getUserPosts(int userId) async {
    try {
      final response = await apiClient.get('${ApiConstants.postsEndpoint}/user/$userId');

      final List<dynamic> postsJson = response['posts'] ?? [];
      return postsJson.map((json) => PostModel.fromJson(json)).toList();
    } catch (e) {
      throw Exception('Failed to fetch user posts: $e');
    }
  }

  Future<List<TodoModel>> getUserTodos(int userId) async {
    try {
      final response = await apiClient.get('${ApiConstants.todosEndpoint}/user/$userId');

      final List<dynamic> todosJson = response['todos'] ?? [];
      return todosJson.map((json) => TodoModel.fromJson(json)).toList();
    } catch (e) {
      throw Exception('Failed to fetch user todos: $e');
    }
  }

  Future<PostModel> createPost({
    required String title,
    required String body,
    required int userId,
  }) async {
    try {
      final response = await apiClient.post(
        '${ApiConstants.postsEndpoint}/add',
        {
          'title': title,
          'body': body,
          'userId': userId,
        },
      );

      return PostModel.fromJson(response);
    } catch (e) {
      throw Exception('Failed to create post: $e');
    }
  }
}
