import 'package:user_management_app/features/users/domain/entities/user.dart';
import 'package:user_management_app/features/users/domain/entities/post.dart';
import 'package:user_management_app/features/users/domain/entities/todo.dart';
import 'package:user_management_app/features/users/domain/repositories/users_repository.dart';
import 'package:user_management_app/features/users/data/datasources/users_remote_data_source.dart';

class UsersRepositoryImpl implements UsersRepository {
  final UsersRemoteDataSource remoteDataSource;

  UsersRepositoryImpl({required this.remoteDataSource});

  @override
  Future<List<User>> getUsers({int limit = 20, int skip = 0}) async {
    try {
      return await remoteDataSource.getUsers(limit: limit, skip: skip);
    } catch (e) {
      throw Exception('Repository error: $e');
    }
  }

  @override
  Future<List<User>> searchUsers(String query) async {
    try {
      return await remoteDataSource.searchUsers(query);
    } catch (e) {
      throw Exception('Repository error: $e');
    }
  }

  @override
  Future<List<Post>> getUserPosts(int userId) async {
    try {
      return await remoteDataSource.getUserPosts(userId);
    } catch (e) {
      throw Exception('Repository error: $e');
    }
  }

  @override
  Future<List<Todo>> getUserTodos(int userId) async {
    try {
      return await remoteDataSource.getUserTodos(userId);
    } catch (e) {
      throw Exception('Repository error: $e');
    }
  }

  @override
  Future<Post> createPost({
    required String title,
    required String body,
    required int userId,
  }) async {
    try {
      return await remoteDataSource.createPost(
        title: title,
        body: body,
        userId: userId,
      );
    } catch (e) {
      throw Exception('Repository error: $e');
    }
  }
}
