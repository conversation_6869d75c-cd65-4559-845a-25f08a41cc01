import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:user_management_app/core/theme/app_theme.dart';
import 'package:user_management_app/features/users/presentation/bloc/users_bloc.dart';
import 'package:user_management_app/features/users/presentation/pages/users_list_page.dart';
import 'package:user_management_app/features/users/data/repositories/users_repository_impl.dart';
import 'package:user_management_app/features/users/data/datasources/users_remote_data_source.dart';
import 'package:user_management_app/core/network/api_client.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => UsersBloc(
            repository: UsersRepositoryImpl(
              remoteDataSource: UsersRemoteDataSource(
                apiClient: ApiClient(),
              ),
            ),
          )..add(LoadUsersEvent()),
        ),
      ],
      child: <PERSON><PERSON><PERSON>(
        title: 'User Management App',
        debugShowCheckedModeBanner: false,
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: ThemeMode.system,
        home: const UsersListPage(),
      ),
    );
  }
}
