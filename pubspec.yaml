name: user_management_app
description: A Flutter app with user management using BLoC pattern and API integration.
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_bloc: ^8.1.3
  equatable: ^2.0.5
  http: ^1.1.0
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0
  pull_to_refresh: ^2.0.0
  shared_preferences: ^2.2.2
  cupertino_icons: ^1.0.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true
  assets:
    - assets/images/
  # fonts:
  #   - family: Wisteriano
  #     fonts:
  #       - asset: assets/fonts/Wisteriano.ttf
  #   - family: Mageline
  #     fonts:
  #       - asset: assets/fonts/Mageline.ttf
  #   - family: Kicaps
  #     fonts:
  #       - asset: assets/fonts/Kicaps.ttf
