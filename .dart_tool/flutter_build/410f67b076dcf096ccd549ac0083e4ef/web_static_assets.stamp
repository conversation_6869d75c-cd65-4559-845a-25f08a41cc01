{"inputs": ["/opt/homebrew/Caskroom/flutter/3.32.1/flutter/bin/cache/engine.stamp"], "outputs": ["/Users/<USER>/development/intenship projects/Flutter-Development-internship/.dart_tool/flutter_build/410f67b076dcf096ccd549ac0083e4ef/flutter.js", "/Users/<USER>/development/intenship projects/Flutter-Development-internship/.dart_tool/flutter_build/410f67b076dcf096ccd549ac0083e4ef/canvaskit/skwasm.js", "/Users/<USER>/development/intenship projects/Flutter-Development-internship/.dart_tool/flutter_build/410f67b076dcf096ccd549ac0083e4ef/canvaskit/skwasm.js.symbols", "/Users/<USER>/development/intenship projects/Flutter-Development-internship/.dart_tool/flutter_build/410f67b076dcf096ccd549ac0083e4ef/canvaskit/canvaskit.js.symbols", "/Users/<USER>/development/intenship projects/Flutter-Development-internship/.dart_tool/flutter_build/410f67b076dcf096ccd549ac0083e4ef/canvaskit/skwasm.wasm", "/Users/<USER>/development/intenship projects/Flutter-Development-internship/.dart_tool/flutter_build/410f67b076dcf096ccd549ac0083e4ef/canvaskit/chromium/canvaskit.js.symbols", "/Users/<USER>/development/intenship projects/Flutter-Development-internship/.dart_tool/flutter_build/410f67b076dcf096ccd549ac0083e4ef/canvaskit/chromium/canvaskit.js", "/Users/<USER>/development/intenship projects/Flutter-Development-internship/.dart_tool/flutter_build/410f67b076dcf096ccd549ac0083e4ef/canvaskit/chromium/canvaskit.wasm", "/Users/<USER>/development/intenship projects/Flutter-Development-internship/.dart_tool/flutter_build/410f67b076dcf096ccd549ac0083e4ef/canvaskit/canvaskit.js", "/Users/<USER>/development/intenship projects/Flutter-Development-internship/.dart_tool/flutter_build/410f67b076dcf096ccd549ac0083e4ef/canvaskit/canvaskit.wasm"]}