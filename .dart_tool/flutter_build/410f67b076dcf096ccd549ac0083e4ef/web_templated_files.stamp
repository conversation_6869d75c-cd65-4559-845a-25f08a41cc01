{"inputs": ["/Users/<USER>/development/intenship projects/Flutter-Development-internship/web/*/index.html", "/Users/<USER>/development/intenship projects/Flutter-Development-internship/web/flutter_bootstrap.js", "/opt/homebrew/Caskroom/flutter/3.32.1/flutter/bin/cache/engine.stamp"], "outputs": ["/Users/<USER>/development/intenship projects/Flutter-Development-internship/build/web/*/index.html", "/Users/<USER>/development/intenship projects/Flutter-Development-internship/build/web/flutter_bootstrap.js"], "buildKey": "[{\"compileTarget\":\"dart2js\",\"renderer\":\"canvaskit\",\"mainJsPath\":\"main.dart.js\"}]"}