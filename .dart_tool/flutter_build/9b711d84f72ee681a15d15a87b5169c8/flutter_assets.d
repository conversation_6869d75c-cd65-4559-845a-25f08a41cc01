 /Users/<USER>/development/intenship\ projects/Flutter-Development-internship/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/assets/images/README.md /Users/<USER>/development/intenship\ projects/Flutter-Development-internship/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf /Users/<USER>/development/intenship\ projects/Flutter-Development-internship/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/fonts/MaterialIcons-Regular.otf /Users/<USER>/development/intenship\ projects/Flutter-Development-internship/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/shaders/ink_sparkle.frag /Users/<USER>/development/intenship\ projects/Flutter-Development-internship/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/AssetManifest.json /Users/<USER>/development/intenship\ projects/Flutter-Development-internship/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/AssetManifest.bin /Users/<USER>/development/intenship\ projects/Flutter-Development-internship/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/FontManifest.json /Users/<USER>/development/intenship\ projects/Flutter-Development-internship/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/NOTICES.Z /Users/<USER>/development/intenship\ projects/Flutter-Development-internship/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/NativeAssetsManifest.json:  /Users/<USER>/development/intenship\ projects/Flutter-Development-internship/pubspec.yaml /Users/<USER>/development/intenship\ projects/Flutter-Development-internship/assets/images/README.md /Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/assets/CupertinoIcons.ttf /opt/homebrew/Caskroom/flutter/3.32.1/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf /opt/homebrew/Caskroom/flutter/3.32.1/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag /Users/<USER>/development/intenship\ projects/Flutter-Development-internship/.dart_tool/flutter_build/9b711d84f72ee681a15d15a87b5169c8/native_assets.json /Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/bloc-8.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_web-1.3.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_bloc-8.1.6/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-3.0.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.9/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/lints-3.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/pull_to_refresh-2.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shimmer-3.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.3.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-15.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/LICENSE /opt/homebrew/Caskroom/flutter/3.32.1/flutter/bin/cache/pkg/sky_engine/LICENSE /opt/homebrew/Caskroom/flutter/3.32.1/flutter/packages/flutter/LICENSE /Users/<USER>/development/intenship\ projects/Flutter-Development-internship/DOES_NOT_EXIST_RERUN_FOR_WILDCARD226344605