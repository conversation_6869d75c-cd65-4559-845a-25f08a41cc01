{"inputs": ["/opt/homebrew/Caskroom/flutter/3.32.1/flutter/packages/flutter_tools/lib/src/build_system/targets/native_assets.dart", "/Users/<USER>/development/intenship projects/Flutter-Development-internship/.dart_tool/package_config_subset"], "outputs": ["/Users/<USER>/development/intenship projects/Flutter-Development-internship/.dart_tool/flutter_build/9b711d84f72ee681a15d15a87b5169c8/native_assets.json", "/Users/<USER>/development/intenship projects/Flutter-Development-internship/.dart_tool/flutter_build/9b711d84f72ee681a15d15a87b5169c8/native_assets.json"]}