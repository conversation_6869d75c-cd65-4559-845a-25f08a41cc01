# User Management App

A Flutter application with user management using BLoC pattern, API integration, and clean code practices.

## Features

- **User List**: Display users with avatar, name, email, and company information
- **Search Functionality**: Real-time search by user name
- **Infinite Scrolling**: Load more users as you scroll
- **User Details**: View detailed user information, posts, and todos
- **Create Posts**: Add new posts locally with title and body
- **Pull to Refresh**: Refresh user list
- **Loading States**: Shimmer loading effects and progress indicators
- **Error Handling**: Comprehensive error handling with retry options
- **Responsive Design**: Mobile-first design with complete responsiveness
- **Dark/Light Theme**: Automatic theme switching based on system preference

## Architecture

This app follows Clean Architecture principles with:

- **Presentation Layer**: BLoC pattern for state management
- **Domain Layer**: Business logic and entities
- **Data Layer**: Repository pattern with remote data sources

## API Integration

Uses DummyJSON API:
- Users: `https://dummyjson.com/users`
- Search: `https://dummyjson.com/users/search`
- Posts: `https://dummyjson.com/posts/user/{userId}`
- Todos: `https://dummyjson.com/todos/user/{userId}`

## Dependencies

- `flutter_bloc`: State management
- `equatable`: Value equality
- `http`: HTTP requests
- `cached_network_image`: Image caching
- `shimmer`: Loading animations
- `pull_to_refresh`: Pull to refresh functionality
- `shared_preferences`: Local storage

## Getting Started

1. Ensure Flutter is installed and configured
2. Clone this repository
3. Run `flutter pub get` to install dependencies
4. Run `flutter run` to start the app

## Project Structure

```
lib/
├── core/
│   ├── constants/
│   ├── network/
│   └── theme/
├── features/
│   ├── users/
│   │   ├── data/
│   │   ├── domain/
│   │   └── presentation/
│   └── posts/
│       ├── data/
│       ├── domain/
│       └── presentation/
└── main.dart
```

## Design Features

- **Premium Typography**: Uses Wisteriano, Mageline, and Kicaps fonts
- **Glassmorphic Effects**: Modern UI with glassmorphic sticky headers
- **Scroll Animations**: Smooth scroll-based animations
- **Material Design 3**: Latest Material Design principles
- **Responsive Layout**: Optimized for all screen sizes
